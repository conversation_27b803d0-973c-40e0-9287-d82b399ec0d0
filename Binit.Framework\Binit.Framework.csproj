﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>netcoreapp3.1</TargetFramework>
        <!--<PublishTrimmed>true</PublishTrimmed>
    <PublishReadyToRun>true</PublishReadyToRun>-->
        <Version>$([System.DateTime]::Now.ToString("yyyy.MM.dd"))</Version>
        <AddRazorSupportForMvc>true</AddRazorSupportForMvc>
        <OutputType>Library</OutputType>
        <EmbeddedResourceUseDependentUponConvention>false</EmbeddedResourceUseDependentUponConvention>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="iTextSharp" Version="5.5.13.1">
            <NoWarn>NU1701</NoWarn>
        </PackageReference>
        <PackageReference Include="itextsharp.xmlworker" Version="5.5.13.1">
            <NoWarn>NU1701</NoWarn>
        </PackageReference>
        <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.2.0" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore"
            Version="3.1.0" />
        <PackageReference Include="FluentEmail.Core" Version="3.0.0" />
        <PackageReference Include="FluentEmail.Razor" Version="3.0.0" />
        <PackageReference Include="FluentEmail.Smtp" Version="3.0.0" />
        <PackageReference Include="Microsoft.CodeAnalysis.FxCopAnalyzers" Version="2.9.8">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Logging" Version="3.1.6" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="3.1.0" />
        <PackageReference Include="Microsoft.Extensions.Localization" Version="3.1.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="3.1.0" />
        <PackageReference Include="Oracle.EntityFrameworkCore" Version="3.19.80" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="3.1.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="3.1.6" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="3.1.6" />
        <PackageReference Include="EPPlus" Version="4.5.3.2" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="3.0.0" />
        <PackageReference Include="PromanagerUI" Version="1.0.0" />
        <PackageReference Include="Quartz" Version="3.0.7" />
        <PackageReference Include="Quartz.Serialization.Json" Version="3.0.7" />
        <!-- <PackageReference Include="Ignite-UIComponents" Version="0.1.0" />  -->
    </ItemGroup>

    <ItemGroup>
        <Content Remove="Helpers\Email\Views\ForgotPassword.cshtml" />
        <Content Remove="Helpers\Email\Views\HappeningNotification.cshtml" />
        <Content Remove="Helpers\Email\Views\InconsistencyReport.cshtml" />
        <Content Remove="Helpers\Email\Views\PasswordRecovery.cshtml" />
        <Content Remove="Helpers\Email\Views\TaskNotification.cshtml" />
        <Content Remove="Helpers\Email\Views\Welcome.cshtml" />
        <Content Remove="Helpers\Email\Views\AlertForLaying.cshtml" />
        <Content Remove="Helpers\Email\Views\AlertForMortality.cshtml" />
        <Content Remove="Helpers\Email\Views\AlertForHenReport.cshtml" />
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Include="Helpers\Email\Views\ForgotPassword.cshtml">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </EmbeddedResource>
        <EmbeddedResource Include="Helpers\Email\Views\PasswordRecovery.cshtml">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </EmbeddedResource>
        <EmbeddedResource Include="Helpers\Email\Views\HappeningNotification.cshtml">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </EmbeddedResource>
        <EmbeddedResource Include="Helpers\Email\Views\InconsistencyReport.cshtml">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </EmbeddedResource>
        <EmbeddedResource Include="Helpers\Email\Views\TaskNotification.cshtml">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </EmbeddedResource>
        <EmbeddedResource Include="Helpers\Email\Views\Welcome.cshtml">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </EmbeddedResource>
        <EmbeddedResource Include="Helpers\Email\Views\Notification.cshtml">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </EmbeddedResource>
        <EmbeddedResource Include="Helpers\Email\Views\AlertForLaying.cshtml">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </EmbeddedResource>
        <EmbeddedResource Include="Helpers\Email\Views\AlertForMortality.cshtml">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </EmbeddedResource>
        <EmbeddedResource Include="Helpers\Email\Views\AlertForHenReport.cshtml">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </EmbeddedResource>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Update="Localization\Resources\SharedResources.es.resx">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </EmbeddedResource>
        <EmbeddedResource Update="Localization\Resources\SharedResources.resx">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </EmbeddedResource>
        <EmbeddedResource Update="Localization\Resources\SharedResources.pt.resx">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </EmbeddedResource>
    </ItemGroup>
</Project>