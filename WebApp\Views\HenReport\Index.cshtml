@using Binit.Framework
@using Binit.Framework.Interfaces.DAL
@using Binit.Framework.Interfaces.Configuration;
@using Microsoft.Extensions.Localization
@using Binit.Framework.Constants.Authentication
@using Domain.Entities.Model;
@using System.Globalization;
@using Domain.Entities.Model.Enum;
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.HenReport.Index
@using RowLang = Binit.Framework.Localization.LocalizationConstants.WebApp.Models.HenReportRow;
@inject IStringLocalizer<SharedResources> localizer;
@inject IOperationContext operationContext;
@inject ISolutionConfiguration solutionConfiguration;
@{
    string lang = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
    List<SelectListItem> henStages = ViewData["HenStages"] as List<SelectListItem>;
    var farms = ViewData["Farms"] as List<SelectListItem>;
    var clusters = ViewData["Clusters"] as List<SelectListItem>;
    var warehouses = ViewData["Warehouses"] as List<SelectListItem>;
    var lines = ViewData["Lines"] as List<SelectListItem>;
    var genetics = ViewData["Genetics"] as List<SelectListItem>;
    var henBatches = ViewData["HenBatches"] as List<SelectListItem>;
    var henStage = ViewData["HenStage"] as HenStage?;
    var users = ViewData["Users"] as Dictionary<Guid, string>;
    var henStageValue = henStage.Value;
    bool moreThanOneFarm = farms.Count > 1;
    bool moreThanOneCluster = clusters.Count > 1;
    bool moreThanOneHenWarehouse = warehouses.Count > 1;
    bool moreThanOneLine = lines.Count() > 1;
    string selectedHenBatch = ViewData["SelectedHenBatch"] as string;
    string selectedLine = ViewData["SelectedLine"] as string;
    string selectedWarehouse = ViewData["SelectedWarehouse"] as string;
    string selectedCluster = ViewData["SelectedCluster"] as string;
    string selectedFarm = ViewData["SelectedFarm"] as string;
    string selectedGenetic = ViewData["SelectedGenetic"] as string;
    string validationError = ViewData["ValidationError"] as string;
    string informativeMessage = ViewData["Message"] as string;
    DateTime fromDate = DateTime.Today.AddDays(-30);
    bool isDevelopment = solutionConfiguration.Development;
    string tableId = ViewData["TableId"] as string;
    bool hasClusters = (bool)ViewData["HasClusters"];
}

@{
    @if (operationContext.UserIsInAnyRole(Roles.BackofficeSuperAdministrator,
    Roles.BackofficeLayingAdministrator, Roles.BackofficeLayingUser,
    Roles.BackofficeLayingHenReportAdministrator, Roles.BackofficeLayingHenReportUser,
    Roles.BackofficeBreedingAdministrator, Roles.BackofficeBreedingUser,
    Roles.BackofficeBreedingHenReportAdministrator, Roles.BackofficeBreedingHenReportUser,
    Roles.BackofficeBreedingDailyReportsAdministrator, Roles.BackofficeBreedingDailyReportsUser,
    Roles.BackofficeDailyReportWithoutDateValidation))
    {
        if (henStage != null)
        {
            if (henStage == HenStage.Breeding)
            {
                <div class="d-flex justify-content-end">
                    <button type="button" class="btn btn-themecolor mr-2"
                        onclick="location.href='@Url.Action("CreateBreeding", "HenReport")'" id="createHenReportButton">
                        <i class="fa fa-plus"></i>
                        @(localizer[Lang.BtnNewFromWarehouse])
                    </button>
                </div>
            }
            else if (henStage == HenStage.Laying)
            {
                <div class="d-flex justify-content-end">
                    <button type="button" class="btn btn-themecolor mr-2"
                        onclick="location.href='@Url.Action("CreateLaying", "HenReport")'" id="createHenReportButton">
                        <i class="fa fa-plus"></i>
                        @(localizer[Lang.BtnNewFromWarehouse])
                    </button>
                </div>
            }
        }
        else
        {
            <div class="d-flex justify-content-end" style="align-items: center">
                <button type="button" class="btn btn-themecolor mr-2"
                    onclick="location.href='@Url.Action("Create", "HenReport", new { henStage = "Breeding" })'" style="margin:5px">
                    <i class="fa fa-plus"></i>
                    @(localizer[Lang.BtnNewBreeding])
                </button>
                <button type="button" class="btn btn-themecolor mr-2"
                    onclick="location.href='@Url.Action("Create", "HenReport", new { henStage = "Laying" })'" style="margin:5px">
                    <i class="fa fa-plus"></i>
                    @(localizer[Lang.BtnNewLaying])
                </button>
            </div>
        }
        <a id="export" class="btn btn-primary excel mr-2">
            <i class="fa fa-file-excel m-l-5"></i>
            @(localizer[Lang.BtnExportAll])
        </a>
    }
}

<input id="henStage" value="@henStage" type="hidden" />

@(
Html.DevExtreme().IgniteDataGrid<HenReportRow>("HenReport", "GetAll", tableId, localizer, true, new
{
    henStage =
henStage
})
.Columns(column =>
{
    column.Add().DataField("henReportId").DataType(GridColumnDataType.String).Visible(false).ShowInColumnChooser(false).AllowSearch(false);

    if (henStage == null)
    {
        column.Add().DataField("henStage").Caption(localizer[Lang.HenStageSelectLabel]).DataType(GridColumnDataType.String).Visible(true).ShowInColumnChooser(true).AllowSearch(false);
    }
    else
    {
        column.Add().DataField("henStage").Caption(localizer[Lang.HenStageSelectLabel]).DataType(GridColumnDataType.String).Visible(false).ShowInColumnChooser(false).AllowSearch(false);
    }
    if (moreThanOneFarm)
    {
        column.Add().DataField("farmCode").DataType(GridColumnDataType.String).Caption(localizer[Lang.FarmCodeLabel]);

        column.Add().DataField("farmName").DataType(GridColumnDataType.String).Caption(localizer[Lang.FarmNameLabel]);
    }

    if (hasClusters)
    {
        column.Add().DataField("cluster").DataType(GridColumnDataType.String).Caption(localizer[Lang.TableColCluster]);
    }

    if (moreThanOneHenWarehouse)
    {
        column.Add().DataField("warehouse").DataType(GridColumnDataType.String).Caption(localizer[Lang.TableColWarehouse]).SortOrder(SortOrder.Asc).SortIndex(1);
    }

    if (moreThanOneLine)
    {
        column.Add().DataField("line").DataType(GridColumnDataType.String).Caption(localizer[Lang.TableColLine]).SortOrder(SortOrder.Asc).SortIndex(2);
    }

    column.Add().DataField("henBatch").DataType(GridColumnDataType.String).Caption(localizer[Lang.TableColHenbatch]).SortOrder(SortOrder.Asc).SortIndex(3);

    column.Add().DataField("genetic").DataType(GridColumnDataType.String).Caption(localizer[Lang.TableColGenetic]).Visible(false);

    column.Add().DataField("date").DataType(GridColumnDataType.Date).Format("dd/MM/yyyy").Caption(localizer[Lang.TableColDate])
    .SortOrder(SortOrder.Desc).SortIndex(0);

    if (henStage == HenStage.Laying)
    {
        column.Add().DataField("eggs").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColEggs]);

        column.Add().DataField("hatchingEggs").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColHatchingEggs]);

        column.Add().DataField("commerciableEggs").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColCommerciableEggs]);

        column.Add().DataField("brokenEggs").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColBrokenEggs]);

        column.Add().DataField("classifiedEggs").DataType(GridColumnDataType.String).Caption(localizer[Lang.TableColClassifiedEggs])
        .CellTemplate(new JS("customListForClassifiedEggs"))
        .Width(300)
        .AllowFiltering(false)
        .AllowGrouping(false)
        .AllowSorting(false)
        .AllowHeaderFiltering(true).HeaderFilter(f => f.DataSource(new JS("headerFilterCustomClassifiedEggs")));
    }

    column.Add().DataField("totalHenAmount").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColTotalHenAmount]);

    column.Add().DataField("henAmountFemale").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColHenAmountFemale]).Visible(false);

    column.Add().DataField("henAmountMale").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColHenAmountMale]).Visible(false);

    column.Add().DataField("deadFemale").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColDeadFemale]).Visible(false);

    column.Add().DataField("deadMale").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColDeadMale]).Visible(false);

    column.Add().DataField("dead").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColDead]);

    column.Add().DataField("depopulateFemale").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColDepopulateFemale]).Visible(false);

    column.Add().DataField("depopulateMale").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColDepopulateMale]).Visible(false);

    column.Add().DataField("depopulate").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColDepopulate]);

    column.Add().DataField("waterConsumption").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColWater]).Visible(false);

    column.Add().DataField("feedIntakeFemale").DataType(GridColumnDataType.Number).Format(f =>
    f.Type(Format.FixedPoint).Precision(2)).Caption(localizer[Lang.TableColFeedFemale]).Visible(false);

    column.Add().DataField("feedIntakeMale").DataType(GridColumnDataType.Number).Format(f =>
    f.Type(Format.FixedPoint).Precision(2)).Caption(localizer[Lang.TableColFeedMale]).Visible(false);

    column.Add().DataField("feedIntakeTotal").DataType(GridColumnDataType.Number).Format(f =>
    f.Type(Format.FixedPoint).Precision(2)).Caption(localizer[Lang.TableColFeed]);

    column.Add().DataField("operator").DataType(GridColumnDataType.String).Caption(localizer[Lang.TableColOperator]);

    column.Add().DataField("uploadEnum")
    .DataType(GridColumnDataType.String)
    .CalculateDisplayValue("uploadEnumName")
    .Caption(localizer[Lang.TableColUpload])
    .AllowGrouping(false)
    .AllowSearch(false)
    .AllowFiltering(false)
    .AllowHeaderFiltering(true).HeaderFilter(filter => filter.DataSource<ReportUploadEnum>("uploadEnum", localizer));

    column.Add().DataField("minTemp").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColTempMin]).Visible(false);

    column.Add().DataField("maxTemp").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColTempMax]).Visible(false);

    column.Add().DataField("humidity").DataType(GridColumnDataType.Number).Caption(localizer[Lang.TableColHumidity]).Visible(false);

    column.Add().DataField("assignedDeaths").DataType(GridColumnDataType.Boolean).Caption(localizer[Lang.TableColAssignedKills]);

    column.Add().DataField("assignedDepopulations").DataType(GridColumnDataType.Boolean).Caption(localizer[Lang.TableColAssignedDepopulations]);

    column.Add().DataField("casualtiesFemale").DataType(GridColumnDataType.String).Caption(localizer[Lang.TableColCasualtiesFemale])
    .CellTemplate(new JS("customListForCasualtiesFemale"))
    .Width(300)
    .AllowFiltering(false)
    .AllowGrouping(false)
    .AllowSorting(false)
    .AllowHeaderFiltering(true).HeaderFilter(f => f.DataSource(new JS("headerFilterCustomCasualtiesFemale")));

    column.Add().DataField("casualtiesMale")
    .DataType(GridColumnDataType.String).Caption(localizer[Lang.TableColCasualtiesMale])
    .CellTemplate(new JS("customListForCasualtiesMale"))
    .Width(300)
    .AllowFiltering(false)
    .AllowGrouping(false)
    .AllowSorting(false)
    .AllowHeaderFiltering(true).HeaderFilter(f => f.DataSource(new JS("headerFilterCustomCasualtiesMale")));

    column.AddEmptyActionsColumn(localizer)
    .Buttons(button =>
    {
        button.Add(new DataTableRedirectAction()
        {
            InternalName = "details",
            Icon = " fa fa-eye",
            Class = "btn-success",
            Url = $"/HenReport/Details?Id={{id}}&henStage={henStage}",
            ShowDisplayName = false,
            DisplayName = localizer[RowLang.Details]
        });

        if (isDevelopment)
        {
            button.Add(new DataTableRedirectAction()
            {
                InternalName = "trackingFood",
                Icon = " fa fa-share-alt",
                Class = "btn-red",
                Url = $"Tracking/Report?id={{id}}&reportType=" + ReportTypeEnum.Production.ToString(),
                DisplayName = localizer[RowLang.TrackingFoodTooltip]
            });
        }

        if (operationContext.UserIsInAnyRole(
    Roles.BackofficeSuperAdministrator,
    Roles.BackofficeLayingAdministrator,
    Roles.BackofficeLayingUser,
    Roles.BackofficeLayingHenReportAdministrator,
    Roles.BackofficeLayingHenReportUser,
    Roles.HenReportLayingAdjustmentApprover,
    Roles.BackofficeBreedingAdministrator,
    Roles.BackofficeBreedingUser,
    Roles.BackofficeBreedingHenReportAdministrator,
    Roles.BackofficeBreedingHenReportUser,
    Roles.BackofficeBreedingDailyReportsAdministrator,
    Roles.BackofficeBreedingDailyReportsUser,
    Roles.HenReportBreedingAdjustmentApprover,
    Roles.BackofficeDailyReportWithoutDateValidation))
        {
            button.Add(new DataTableRedirectAction()
            {
                InternalName = "establishDeathAndDepopulationQuantitiesReasons",
                Icon = " fa fa-clipboard-list",
                Class = "btn-red",
                Url = $"/HenReport/EstablishDeathAndDepopulationQuantitiesReasons?henStage={henStage}&henReportId={{id}}",
                DisplayName = localizer[RowLang.EstablishDeathQuantitiesReasons]
            });

            // Cannot go into MASTER until testing is finished.
            //button.Add(new DataTableDecisionAction()
            //{
            // InternalName = "delete",
            // Class = "btn-danger",
            // Icon = "trash",
            // Modal = new SweetAlert(localizer)
            // {
            // Message = string.Format(localizer[RowLang.DeleteQuestionMessage], $"{{name}}"),
            // TypeOfAlert = "warning"
            // },
            // Url = $"/HenReport/Delete/{{id}}",
            // ShowDisplayName = false,
            // DisplayName = localizer[RowLang.DeleteTooltip],
            // Type = "GET",
            // SuccessTitle = localizer[RowLang.DeleteConfirmationMessage]
            //},
            //"isCurrentWeekWithoutAdjustment");
        }
        if (operationContext.UserIsInAnyRole(Roles.BackofficeAdjustButton))
        {
            button.Add(new DataTableRedirectAction()
            {
                InternalName = "edit",
                Icon = " fa fa-edit",
                Class = "btn-primary",
                Url = $"/HenReport/Edit?henReportId={{id}}&henStage={henStage}&shouldValidateCreateAndApprove=false",
                ShowDisplayName = false,
                DisplayName = localizer[RowLang.Edit]
            },
        "isCurrentWeekWithoutAdjustment");

            button.Add(new DataTableRedirectAction()
            {
                InternalName = "ReviewRectification",
                Icon = " fa fa-edit",
                Class = "btn-primary",
                Url = $"/ReportRectification/ReviewRectification?reportId={{id}}",
                ShowDisplayName = false,
                DisplayName = localizer[RowLang.ApproveRectification]
            },
        "isCurrentWeekWithAdjustment");
        }

    });
})

)@Html.AntiForgeryToken()

@section scripts {
    <!-- Script required to localize datatable resources -->
    <script>
        const tableId = '@tableId';
        const btnExport = @Html.Raw(Json.Serialize(localizer[Lang.BtnExportAll]));
        const validationError = '@Html.Raw(validationError)';
        const informativeMessage = '@Html.Raw(informativeMessage)';
        const title = '@Html.Raw(ViewData["Title"])';
        const henStage = '@henStage';
    </script>

    <script src="@Url.Content("~/js/views/henReport/index.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/views/henReport/actionButtonVisibility.js")" type="text/javascript"></script>

}

<ignite-load plugins="select2,date-time-picker,dropzone"></ignite-load>
