using Binit.Framework.Interfaces.Email;
using System;
using System.Collections.Generic;
using Binit.Framework.Constants.Email;
using Binit.Framework.Interfaces.Configuration;
using Microsoft.Extensions.Localization;
using Lang = Binit.Framework.Localization.LocalizationConstants.BinitFramework.Helpers.Email.Views.AlertForHenReport;

namespace Binit.Framework.Helpers.Email.DTOs
{
    public class AlertForHenReportDTO : IEmailDTO
    {
        public AlertForHenReportDTO(IGeneralConfiguration generalConfiguration, IStringLocalizer localizer, string name, string reportDate)
        {
            this.SystemName = generalConfiguration.SystemName;
            this.Title = localizer[Lang.Title];
            this.Subject = localizer[Lang.Subject, name];
            this.Intro = localizer[Lang.Intro];
            this.FirstContent = localizer[Lang.FirstContent, name, reportDate];
            this.SecondContent = localizer[Lang.SecondContent];
            this.SecondContentBold = localizer[Lang.SecondContentBold];
            this.SecondContentFinal = localizer[Lang.SecondContentFinal];
            this.SystemUrl = generalConfiguration.SystemUrl;
            this.CallToActionText = null;
            this.CallToActionButton = null;
        }

        public TemplateConstants.TemplateEnum Template => TemplateConstants.TemplateEnum.AlertForHenReport;

        public string Name { get; set; }

        public string SystemUrl { get; set; }
        public string SystemName { get; set; }

        public string Title { get; set; }

        public string Subject { get; set; }

        public string Intro { get; set; }

        public string FirstContent { get; set; }
        public string SecondContent { get; set; }
        public string SecondContentBold { get; set; }
        public string SecondContentFinal { get; set; }
        public string Footer { get; set; }

        public string CallToActionText { get; set; }

        public string CallToActionButton { get; set; }

    }
}
