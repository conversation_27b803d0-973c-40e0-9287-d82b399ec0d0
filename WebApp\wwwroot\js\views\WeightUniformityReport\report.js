// function that repopulates the combo box for hen batches
const henStage =
  new URLSearchParams(window.location.search).get("henStage") || "Breeding";

let femaleSummaryData = {};
let maleSummaryData = {};

let femaleData = {};
let maleData = {};

const getActiveParameter = () => {
  switch ($("#henbatch-status").val()) {
    case "active":
      return true;
    case "closed":
      return false;
    default:
      return null;
  }
};

let refreshParentHenBatchesByFarm = async () => {
  let selectedFarm = $("#farm").val();
  $("#parentHenBatch option").not(":first").remove();
  $("#henBatch option").not(":first").remove();
  let active = getActiveParameter();

  if (henStage !== "" && selectedFarm !== "") {
    const parentHenBatches = await fetch(
      `${location.origin}/WeightUniformityReport/GetParentHenBatches?henStage=${henStage}&selectedFarm=${selectedFarm}&active=${active}`
    ).then((res) => res.json());

    if (parentHenBatches.length > 0) {
      $.each(parentHenBatches, function (i, l) {
        $("#parentHenBatch").append(
          new Option(l.text, l.value, false, l.selected)
        );
      });
    } else {
      swal({
        type: "info",
        text: noHenBatches,
      });
    }
  }

  selectedFarm == "";
};

// on change methods for all combo boxes in the view
$("#farm").on("change", refreshParentHenBatchesByFarm);
$("#henbatch-status").on("change", refreshParentHenBatchesByFarm);

// Função para controlar a visibilidade das tabelas e gráficos
function controlTableVisibility(showMale = true) {
  const maleTableContainer = document.getElementById("male-table-container");
  const femaleTableContainer = document.getElementById(
    "female-table-container"
  );

  // Controla visibilidade das tabelas
  maleTableContainer.style.display = showMale ? "block" : "none";
  femaleTableContainer.style.display = showMale ? "none" : "block";
}

function renderMaleTable() {
  //Renderiza o summary
  document.getElementById("batch-info").innerHTML = "";
  document.getElementById("batch-info").innerHTML = `
            <div class="batch-info-inline">
                <span>Geral do lote: ${
                  maleData.summary.batch
                }</span>&nbsp;&nbsp;
                <span>Data de Alojamento: ${new Date(
                  maleData.summary.placementDate
                ).toLocaleDateString("pt-BR")}</span>&nbsp;&nbsp;
                <span>Aves Alojadas: ${
                  maleData.summary.initialAmount
                }</span>&nbsp;&nbsp;
                <span>Saldo Atual: ${maleData.summary.currentAmount}</span>
            </div>
        `;

  // Renderiza a tabela masculina usando os dados armazenados
  let maleContainer = document.getElementById("male-table");
  maleContainer.innerHTML = "";
  let maleHot = new Handsontable(maleContainer, {
    data: maleData.data,
    nestedHeaders: maleData.headers,
    columns: [
      { type: "text", readOnly: true },
      { type: "numeric", readOnly: true },
      ...Array(
        maleData.headers[0].filter((h) => h && h.colspan).length * 3
      ).fill({
        type: "numeric",
        readOnly: true,
      }),
    ],
    readOnly: true,
    licenseKey: "non-commercial-and-evaluation",
    rowHeaders: false,
    filters: false,
    fillHandle: false,
    dropdownMenu: false,
    autoColumnSize: true,
    manualColumnSize: true,
    manualColumnMove: false,
    width: "100%",
    height: 400,
    fixedRowsTop: 0,
    fixedColumnsLeft: 0,
    fixedRowsBottom: 0,
    fixedColumnsRight: 0,
    scrollbarH: true,
    scrollbarV: true,
    outsideClickDeselects: false,
    renderAllRows: false,
    viewportColumnRenderingOffset: 100,
    viewportRowRenderingOffset: 100,
  });
  maleHot.render();

  // Atualiza a visibilidade
  controlTableVisibility(true);

  // Atualiza o estado visual dos botões
  $("#btn-male")
    .addClass("active")
    .removeClass("btn-secondary")
    .addClass("btn-primary");
  $("#btn-female")
    .removeClass("active")
    .removeClass("btn-primary")
    .addClass("btn-secondary");
}

function renderFemaleTable() {
  //Renderiza o summary
  document.getElementById("batch-info").innerHTML = "";
  document.getElementById("batch-info").innerHTML = `
                <div class="batch-info-inline">
                    <span>Geral do lote: ${
                      femaleData.summary.batch
                    }</span>&nbsp;&nbsp;
                    <span>Data de Alojamento: ${new Date(
                      femaleData.summary.placementDate
                    ).toLocaleDateString("pt-BR")}</span>&nbsp;&nbsp;
                    <span>Aves Alojadas: ${
                      femaleData.summary.initialAmount
                    }</span>&nbsp;&nbsp;
                    <span>Saldo Atual: ${
                      femaleData.summary.currentAmount
                    }</span>
                </div>
            `;

  // Renderiza a tabela feminina usando os dados armazenados
  let femaleContainer = document.getElementById("female-table");
  femaleContainer.innerHTML = "";
  let femaleHot = new Handsontable(femaleContainer, {
    data: femaleData.data,
    nestedHeaders: femaleData.headers,
    columns: [
      { type: "text", readOnly: true },
      { type: "numeric", readOnly: true },
      ...Array(
        femaleData.headers[0].filter((h) => h && h.colspan).length * 3
      ).fill({
        type: "numeric",
        readOnly: true,
      }),
    ],
    readOnly: true,
    licenseKey: "non-commercial-and-evaluation",
    rowHeaders: false,
    filters: false,
    fillHandle: false,
    dropdownMenu: false,
    autoColumnSize: true,
    width: "100%",
    height: 400,
    manualColumnSize: true,
    manualColumnMove: false,
    fixedRowsTop: 0,
    fixedColumnsLeft: 0,
    fixedRowsBottom: 0,
    fixedColumnsRight: 0,
    scrollbarH: true,
    scrollbarV: true,
    outsideClickDeselects: false,
    renderAllRows: false,
    viewportColumnRenderingOffset: 100,
    viewportRowRenderingOffset: 100,
  });

  femaleHot.render();

  // Atualiza a visibilidade
  controlTableVisibility(false);

  // Atualiza o estado visual dos botões
  $("#btn-female")
    .addClass("active")
    .removeClass("btn-secondary")
    .addClass("btn-primary");
  $("#btn-male")
    .removeClass("active")
    .removeClass("btn-primary")
    .addClass("btn-secondary");
}

// Atualiza os event listeners dos botões
$("#btn-male").click(function () {
  renderMaleTable();
});

$("#btn-female").click(function () {
  renderFemaleTable();
});

// Modifica o evento do botão de filtro
$("#btn-filter").click(function () {
  const $button = $(this);
  const originalContent = $button.html();

  $button
    .prop("disabled", true)
    .html('<i class="fa fa-spinner fa-spin m-l-5"></i> Carregando...');

  $.ajax({
    type: "GET",
    url: "/WeightUniformityReport/ReportData",
    data: {
      farm: $("#farm").val(),
      parentHenBatch: $("#parentHenBatch").val(),
      active: getActiveParameter(),
      henStage: henStage,
    },
    success: function (response) {
      // Armazena os dados globalmente
      maleData = response.male;
      femaleData = response.female;

      const buttons = document.getElementById("btn-tables-selector");
      buttons.style.display = "block";

      if (response.male.data?.length) {
        const btnMale = document.getElementById("btn-male");
        btnMale.disabled = false;
        btnMale.classList.remove("btn-secondary");
        btnMale.classList.add("btn-primary");
        btnMale.style.cursor = "pointer";
      }
      if (response.female.data?.length) {
        const btnFemale = document.getElementById("btn-female");
        btnFemale.disabled = false;
        btnFemale.classList.remove("btn-secondary");
        btnFemale.classList.add("btn-primary");
        btnFemale.style.cursor = "pointer";
      }

      if (response.male.data?.length) {
        renderMaleTable();
      } else if (response.female.data?.length) {
        renderFemaleTable();
      }
    },
    error: function (xhr, status, error) {
      Swal.fire({
        icon: "error",
        title: "Erro ao carregar dados",
        text: "Não foi possível carregar os dados do relatório. Por favor, tente novamente.",
      });
    },
    complete: function () {
      $button.prop("disabled", false).html(originalContent);
    },
  });
});

$("#btn-export-filtered").click(function () {
  const $button = $(this);
  const originalContent = $button.html();

  // Show loading state
  $button
    .prop("disabled", true)
    .html('<i class="fa fa-spinner fa-spin m-l-5"></i> Carregando...');

  // Use AJAX to handle the server response
  $.ajax({
    type: "POST",
    url: "/WeightUniformityReport/ExcelExport",
    contentType: "application/json",
    data: JSON.stringify({
      farm: $("#farm").val(),
      parentHenBatch: $("#parentHenBatch").val(),
      active: getActiveParameter(),
      henStage: henStage,
    }),
    xhrFields: {
      responseType: "blob",
    },
    success: function (response) {
      const blob = new Blob([response], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "WeightUniformityReport.xlsx";
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    },
    error: function (xhr, status, error) {
      Swal.fire({
        icon: "Error",
        title: "Esse lote não possui relatórios",
        text: "Por favor, tentar outro lote.",
      });
    },
    complete: function () {
      // Restore button state
      $button.prop("disabled", false).html(originalContent);
    },
  });
});
