@using Binit.Framework
@using Binit.Framework.Helpers
@using Domain.Entities.Model
@using Microsoft.Extensions.Localization
@using Lang = Binit.Framework.Localization.LocalizationConstants.WebApp.Views.WeightUniformityReport.Report

@inject IStringLocalizer<SharedResources> localizer

@{
    var farms = ViewData["Farms"] as List<SelectListItem>;
    var parentHenBatches = ViewData["ParentHenBatches"] as List<SelectListItem>;
    var status = ViewData["HenBatchStatus"] as List<SelectListItem>;
}

<link href="~/css/handsontable.css" rel="stylesheet" />
<style>
    .handsontable-container {
        height: 400px;
    }


    .ht_clone_top {
        z-index: 1 !important;
    }


    .male-table-container,
    .female-table-container {
        margin-bottom: 20px;
    }

    .batch-info-inline {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
        background-color: #ffa500;
        padding: 2px;
    }

    #batch-info {
        padding-top: 10px;
    }

    /* Estilização tabela */
    /* Estilo para células de porcentagem (amarelo) */
    .handsontable .htCore td:nth-child(3n+1) {
        background-color: #FFFF00;
    }

    /* Estilo para cabeçalhos */
    .handsontable .htCore th {
        background-color: #FF0000;
        color: white;
        font-weight: bold;
    }

    .handsontable .htCore td {
        white-space: nowrap;
    }

    .handsontable .htCore th:nth-child(1) {
        background-color: #ffa500;
        color: black;
    }

    /* Estilo para cabeçalho de idade */
    .handsontable .htCore td:nth-child(2) {
        background-color: #78CCFD;
        text-align: center;
        color: black;
        /*writing-mode: vertical-rl;
        text-orientation: mixed;
        transform: rotate(180deg);*/
    }

    /* Estilo para cabeçalho de data (primeira coluna) */
    .handsontable .htCore th:nth-child(1) {
        background-color: transparent !important;
        color: black;
    }

    /* Estilo para cabeçalho de idade (segunda coluna) */
    .handsontable .htCore td:nth-child(2) {
        background-color: #78CCFD !important;
        text-align: center;
        color: black;
    }

    /* Remove background e bordas apenas da primeira e segunda linha da primeira coluna */
    .handsontable .htCore tr:nth-child(1) th:nth-child(1),
    .handsontable .htCore tr:nth-child(2) th:nth-child(1),
    .handsontable .htCore tr:nth-child(1) th:nth-child(2),
    .handsontable .htCore tr:nth-child(2) th:nth-child(2) {
        background-color: transparent !important;
        border: none !important;
    }

    /* Mantém o background laranja para a terceira linha da primeira coluna */
    .handsontable .htCore tr:nth-child(3) th:nth-child(1) {
        background-color: #ffa500 !important;
    }

    /* Mantém o background vermelho para as outras células do cabeçalho */
    .handsontable .htCore th:not(:nth-child(1)) {
        background-color: #ff0000 !important;
        color: white;
    }

    /* Esconde os botões inicialmente */
    #btn-tables-selector {
        display: none;
    }
</style>

<div class="column">
    <div class="row">
        <div class="form-group col-md-3" style="display:@( farms?.Count() == 1 ? "none": "" )">
            <label for="farm">
                @(localizer[Lang.FarmSelectLabel])
            </label>
            <select class="form-control select2" id="farm">
                <option value="">
                    @(localizer[Lang.FarmSelectPlaceholder])
                </option>
                @foreach (var item in farms)
                {
                    if (farms.Count() == 1)
                    {
                        <option selected value="@item.Value">@item.Text</option>
                    }
                    else
                    {
                        <option value="@item.Value">@item.Text</option>
                    }
                }
            </select>
        </div>

        <div class="form-group col-md-3">
            <label for="henBatch-status">
                @(localizer[Lang.HenBatchStatus])
            </label>
            <select class="form-control select2" id="henbatch-status">
                @foreach (var item in status)
                {
                    if (@item.Selected)
                    {
                        <option selected value="@item.Value">@item.Text</option>

                    }
                    else
                    {
                        <option value="@item.Value">@item.Text</option>
                    }
                }
            </select>
        </div>


        <div class="form-group col-md-3">
            <label for="parentHenBatch">
                @(localizer[Lang.HenBatchSelectLabel])
            </label>
            <select class="form-control select2" id="parentHenBatch">
                <option value="">
                    @(localizer[Lang.HenBatchSelectPlaceholder])
                </option>
                @if (farms.Count() == 1)
                {
                    foreach (var item in parentHenBatches)
                    {
                        if (parentHenBatches.Count() == 1)
                        {
                            <option selected value="@item.Value">@item.Text</option>
                        }
                        else
                        {
                            <option value="@item.Value">@item.Text</option>
                        }
                    }
                }
            </select>
        </div>

        <div class="justify-content-center align-items-center" style="display: flex">
            <button type="button" id="btn-filter" class="btn btn-primary m-l-5 m-b-0">
                Filtrar
            </button>

            <button type="button" id="btn-export-filtered" class="btn btn-primary excel m-l-5 m-b-0">
                <i class="fa fa-file-excel m-l-5"></i> Excel
            </button>
        </div>
    </div>
    <div id="btn-tables-selector">
        <button type="button" id="btn-male" disabled class="btn btn-secondary m-l-5 m-b-0" style="cursor: not-allowed;">
            Machos
        </button>
        <button type="button" id="btn-female" disabled class="btn btn-secondary m-l-5 m-b-0"
            style="cursor: not-allowed;">
            Fêmeas
        </button>
    </div>
    <div id="batch-info"></div>
    <div id="male-table-container" class="male-table-container">
        <div class="handsontable-container">
            <div id="male-table" class="mt-4"></div>
        </div>
    </div>
    <div id="female-table-container" class="female-table-container">
        <div class="handsontable-container">
            <div id="female-table" class="mt-4"></div>
        </div>
    </div>
</div>

@section scripts {
    <script>
        var noHenBatches = '@Html.Raw(localizer[Lang.NoHenBatches].Value)';
    </script>
    <script src="~/js/handsontable.js"></script>
    <script src="~/js/views/weightUniformityReport/report.js"></script>
}

<ignite-load plugins="select2"></ignite-load>